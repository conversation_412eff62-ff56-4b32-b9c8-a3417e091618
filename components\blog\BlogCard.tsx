import Image, { StaticImageData } from "next/image";
import Link from "next/link";

interface BlogCardProps {
  id: number;
  image: StaticImageData;
  title: string;
  date: string;
}

export function BlogCard({ id, image, title, date }: BlogCardProps) {
  return (
    <Link href={`/blog/${id}`} className="group relative flex flex-col gap-5">
      <div className="relative overflow-hidden rounded-3xl">
        <Image src={image} alt={title} className="w-full" />
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
          <button className="hover:bg-opacity-90 cursor-pointer rounded-full bg-white px-6 py-2 font-medium text-black transition-colors">
            اقرأ المزيد
          </button>
        </div>
      </div>
      <div className="text-[14px] font-[500] text-[#999999] lg:text-[16px]">
        {date}
      </div>
      <p className="text-[20px] font-[600] lg:text-[24px]">{title}</p>
    </Link>
  );
}
