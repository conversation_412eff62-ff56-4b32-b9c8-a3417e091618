"use client";

import { CheckCircle, Copy } from "lucide-react";
import { useState } from "react";

import discount from "@/assets/discount-shape.svg";
import Image from "next/image";
import { Card } from "../ui/card";

type CouponItemProps = {
  title: string;
  subTitle: string;
  coupon: string;
};

export default function CouponItem({
  title,
  subTitle,
  coupon,
}: CouponItemProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText("IMI");
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <>
      <Card className="relative gap-0 py-0 shadow-none">
        <div className="flex items-start justify-between p-4">
          <div className="flex flex-col gap-1">
            <p className="text-[14px] font-[600] lg:text-[16px]">{title}</p>
            <p className="text-[10px] font-[600] text-[#646464] lg:text-[14px]">
              {subTitle}
            </p>
          </div>

          <div className="flex items-center gap-1 rounded-full bg-[#FFE9E9] px-4 py-1 text-sm text-black">
            <Image src={discount} alt="discount" />
            <span className="font-[600]">كود خصم</span>
          </div>
        </div>

        <div className="absolute top-1/2 left-0 w-full border-t border-dashed border-gray-300"></div>

        <div className="relative p-4 text-center">
          {copied && (
            <div className="absolute top-[-16px] left-1/2 flex -translate-x-1/2 items-center gap-2 rounded-full bg-black px-4 py-1 text-sm text-white">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="font-[600]">تم النسخ</span>
            </div>
          )}

          <div className="flex items-center justify-between rounded-full bg-[#F6F6F6] p-2">
            <button
              onClick={handleCopy}
              className="flex cursor-pointer items-center gap-2 rounded-full bg-black px-4 py-2 text-white"
            >
              <Copy className="h-4 w-4" />
              <span className="text-[12px] font-[600] lg:text-[14px]">
                نسخ الكود
              </span>
            </button>
            <span className="mx-auto text-xl text-[14px] font-bold lg:text-[16px]">
              {coupon}
            </span>
          </div>
        </div>

        <div className="absolute top-[50%] -left-[10px] h-5 w-5 -translate-y-1/2 rounded-full border-r bg-white" />
        <div className="absolute top-[50%] -right-[10px] h-5 w-5 -translate-y-1/2 rounded-full border-l bg-white" />
      </Card>
    </>
  );
}
