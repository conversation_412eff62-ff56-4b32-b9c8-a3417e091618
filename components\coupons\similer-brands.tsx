import BrandItem from "../common/brand-item";
import { Card } from "../ui/card";

import store_1 from "@/assets/store_1.svg";
import store_2 from "@/assets/store_2.svg";
import store_3 from "@/assets/store_3.svg";
import store_4 from "@/assets/store_4.svg";
import store_5 from "@/assets/store_5.svg";
import store_6 from "@/assets/store_6.svg";
import store_7 from "@/assets/store_7.svg";
import store_8 from "@/assets/store_8.svg";

type Store = {
  id: number;
  image: string;
  title: string;
};

const stores: Store[] = [
  {
    id: 1,
    image: store_1,
    title: "نون",
  },
  {
    id: 2,
    image: store_2,
    title: "ترينديول",
  },
  {
    id: 3,
    image: store_3,
    title: "اي هيرب",
  },
  {
    id: 4,
    image: store_4,
    title: "نمشي",
  },
  {
    id: 5,
    image: store_5,
    title: "ممزورلد",
  },
  {
    id: 6,
    image: store_6,
    title: "6 ستريت",
  },
  {
    id: 7,
    image: store_7,
    title: "ايوا",
  },
  {
    id: 8,
    image: store_8,
    title: "سيار",
  },
];

export default function SimilerBrands() {
  return (
    <Card className="flex-1 rounded-[24px] border-0 shadow-none lg:border lg:p-10">
      <h2 className="text-[24px] font-[600] lg:text-[32px]">متاجر مشابهة</h2>

      <div className="grid grid-cols-1 gap-7 sm:grid-cols-2">
        {stores.map(({ id, image, title }) => {
          return <BrandItem key={id} id={id} image={image} title={title} />;
        })}
      </div>
    </Card>
  );
}
