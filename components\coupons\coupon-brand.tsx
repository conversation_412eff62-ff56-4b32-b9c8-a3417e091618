import brand_image from "@/assets/store_1.svg";
import { ChevronLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "../ui/button";
import { Card } from "../ui/card";

export default function CouponBrand() {
  return (
    <Card className="rounded-[24px] p-5 shadow-none lg:p-10">
      <div className="flex items-center gap-5">
        <Image src={brand_image} alt="brand" />
        <div>
          <h1 className="text-[16px] font-[600] lg:text-[22px]">نون</h1>
          <p className="text-[14px] font-[500] text-[#646464] lg:text-[20px]">
            متجر إلكتروني متكامل لجميع احتياجاتك المنزلية والشخصية.
          </p>
        </div>
      </div>
      <Link href={"/"}>
        <Button className="h-[45px] w-full cursor-pointer rounded-full text-[14px] font-[600] lg:text-[16px]">
          الانتقال للمتجر
          <ChevronLeft />
        </Button>
      </Link>
    </Card>
  );
}
