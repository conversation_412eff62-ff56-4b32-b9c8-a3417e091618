import { Card } from "../ui/card";
import CouponItem from "./coupon-item";

const list = [
  {
    id: 1,
    title: "لا تفوّت الفرصة!",
    subTitle: "خصم 10% لكل المنتجات",
    coupon: "IMI",
  },
  {
    id: 2,
    title: "تسوق وخلّي الخصم علينا!",
    subTitle: "خصم 20%",
    coupon: "IMI",
  },
  {
    id: 3,
    title: "خصم ناري! 🔥",
    subTitle: "خصم 15% لكل المنتجات",
    coupon: "IMI",
  },
];

export default function CouponsList() {
  return (
    <Card className="rounded-[24px] p-5 shadow-none lg:p-10">
      <h3 className="text-[14px] font-[600] lg:text-[20px]">الخصومات🔥</h3>
      {list.map(({ id, title, subTitle, coupon }) => {
        return (
          <CouponItem
            key={id}
            title={title}
            subTitle={subTitle}
            coupon={coupon}
          />
        );
      })}
    </Card>
  );
}
